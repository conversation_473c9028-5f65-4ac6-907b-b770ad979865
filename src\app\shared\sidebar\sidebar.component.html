<div class="sidebar-container" #sidebarContainer>
  <div class="menu-items">
    <!-- Airports of the World item -->
<div 
      class="menu-item home-item"
      role="menuitem"
      tabindex="0"
       [class.active]="isActive('/airports')"
       (click)="navigateToHome()"
      (keydown.enter)="navigateToHome()"
      (keydown.space)="navigateToHome()"
      aria-label="Navigate to Airports of the World"
     >
      <span class="menu-text">Airports of the World</span>
      <mat-icon class="chevron-icon">chevron_right</mat-icon>
    </div>
    
    <!-- Airport items -->
    <div 
      class="menu-item" 
      [class.active]="isAirportActive(airport.key)"
      *ngFor="let airport of airports$ | async" 
      (click)="navigateToAirport(airport)"
    >
      <span class="menu-text">{{ airport.name }}</span>
      <mat-icon class="chevron-icon">chevron_right</mat-icon>
    </div>
  </div>
</div>
