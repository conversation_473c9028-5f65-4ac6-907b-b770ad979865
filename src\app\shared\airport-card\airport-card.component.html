<mat-card class="airport-card" (click)="onCardClick()">
  <div class="airport-name">
    <div class="airport-description">{{ airport.name }}</div>
    <div class="city-code">{{ airport.key }}</div>
  </div>
  
  <div class="airport-details">
    <div class="detail-item">
      <span class="detail-label">City: {{ airport.city }}</span>
    </div>
    
    <div class="detail-item">
      <span class="detail-label">Country: {{ airport.country }}</span>
    </div>
  </div>
</mat-card>
