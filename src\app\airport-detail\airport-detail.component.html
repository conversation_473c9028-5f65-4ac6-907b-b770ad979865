<app-header></app-header>

<div class="airport-detail-container">
  <!-- Sidebar -->
  <app-sidebar class="sidebar"></app-sidebar>

  <!-- Content -->
  <div class="content">
    <!-- Loading spinner -->
    <div class="loading-container" *ngIf="loading">
      <mat-spinner aria-label="Cargando contenido"></mat-spinner>
      <p aria-live="polite">Cargando detalle del aeropuerto...</p>
    </div>

    <!-- Error message -->
    <div class="error-container" *ngIf="error && !loading">
      <mat-icon class="error-icon" aria-hidden="true">error</mat-icon>
      <p class="error-message" role="alert" aria-live="assertive">{{ error }}</p>
      <div class="error-actions">
        <button mat-raised-button color="primary" (click)="retry()">
          Reintentar
        </button>
        <button mat-button (click)="goBack()">
          Volver a la lista
        </button>
      </div>
    </div>

    <!-- Airport detail -->
    <div class="airport-detail" *ngIf="airport && !loading && !error">
      <!-- Title -->
      <h1 class="airport-title">{{ getAirportDisplayName() }}</h1>

      <!-- Image -->
      <div class="airport-image">
        <img [src]="getAirportImage()" [alt]="airport.name" class="img">
      </div>

      <!-- Paragraphs -->
      <div class="airport-description">
        <p class="paragraph">
          {{ getAirportDescription1() }}
        </p>
        <p class="paragraph">
          {{ getAirportDescription2() }}
        </p>
      </div>

      <!-- Airport Card -->
      <div class="airport-card">
        <div class="airport-name">
          <div class="airport-description-item">{{ airport.name }}</div>
          <div class="city-code">{{ airport.key }}</div>
        </div>
        
        <div class="city">
          <div class="city-description">City: {{ airport.city }}</div>
        </div>
        
        <div class="country">
          <div class="country-description">Country: {{ airport.country }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-footer></app-footer>
