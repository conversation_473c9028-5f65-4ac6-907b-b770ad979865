import { TestBed } from '@angular/core/testing';
import { AuthService, User } from './auth.service';

describe('AuthService', () => {
  let service: AuthService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(AuthService);
    // Limpiar localStorage antes de cada test
    localStorage.clear();
  });

  afterEach(() => {
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', () => {
      const username = 'testuser';
      const securityKey = 'testkey';

      const result = service.login(username, securityKey);

      expect(result).toBe(true);
      expect(service.isLoggedIn()).toBe(true);
      expect(service.getCurrentUser()).toEqual({ username, securityKey });
    });

    it('should fail login with empty username', () => {
      const result = service.login('', 'testkey');

      expect(result).toBe(false);
      expect(service.isLoggedIn()).toBe(false);
      expect(service.getCurrentUser()).toBeNull();
    });

    it('should fail login with empty securityKey', () => {
      const result = service.login('testuser', '');

      expect(result).toBe(false);
      expect(service.isLoggedIn()).toBe(false);
      expect(service.getCurrentUser()).toBeNull();
    });

    it('should save user to localStorage on successful login', () => {
      const username = 'testuser';
      const securityKey = 'testkey';

      service.login(username, securityKey);

      const savedUser = localStorage.getItem('currentUser');
      expect(savedUser).toBeTruthy();
      expect(JSON.parse(savedUser!)).toEqual({ username, securityKey });
    });
  });

  describe('logout', () => {
    it('should logout successfully', () => {
      // Primero hacer login
      service.login('testuser', 'testkey');
      expect(service.isLoggedIn()).toBe(true);

      // Luego logout
      service.logout();

      expect(service.isLoggedIn()).toBe(false);
      expect(service.getCurrentUser()).toBeNull();
      expect(localStorage.getItem('currentUser')).toBeNull();
    });
  });

  describe('isLoggedIn', () => {
    it('should return false when not logged in', () => {
      expect(service.isLoggedIn()).toBe(false);
    });

    it('should return true when logged in', () => {
      service.login('testuser', 'testkey');
      expect(service.isLoggedIn()).toBe(true);
    });
  });

  describe('getCurrentUser', () => {
    it('should return null when not logged in', () => {
      expect(service.getCurrentUser()).toBeNull();
    });

    it('should return user when logged in', () => {
      const user = { username: 'testuser', securityKey: 'testkey' };
      service.login(user.username, user.securityKey);
      expect(service.getCurrentUser()).toEqual(user);
    });
  });

  describe('getSecurityKey', () => {
    it('should return null when not logged in', () => {
      expect(service.getSecurityKey()).toBeNull();
    });

    it('should return security key when logged in', () => {
      const securityKey = 'testkey';
      service.login('testuser', securityKey);
      expect(service.getSecurityKey()).toBe(securityKey);
    });
  });

  describe('localStorage persistence', () => {
    it('should restore user from localStorage on service creation', () => {
      const user: User = { username: 'testuser', securityKey: 'testkey' };
      localStorage.setItem('currentUser', JSON.stringify(user));

      // Crear nuevo servicio para simular recarga de página
      const newService = new AuthService();

      expect(newService.isLoggedIn()).toBe(true);
      expect(newService.getCurrentUser()).toEqual(user);
    });

    it('should handle invalid localStorage data gracefully', () => {
      localStorage.setItem('currentUser', 'invalid-json');

      // Esto no debería lanzar error
      expect(() => new AuthService()).not.toThrow();
    });
  });

  describe('currentUser$ observable', () => {
    it('should emit user changes', (done) => {
      const user = { username: 'testuser', securityKey: 'testkey' };
      
      service.currentUser$.subscribe(currentUser => {
        if (currentUser) {
          expect(currentUser).toEqual(user);
          done();
        }
      });

      service.login(user.username, user.securityKey);
    });

    it('should emit null on logout', (done) => {
      service.login('testuser', 'testkey');
      
      let emissionCount = 0;
      service.currentUser$.subscribe(currentUser => {
        emissionCount++;
        if (emissionCount === 2) { // Segunda emisión después del logout
          expect(currentUser).toBeNull();
          done();
        }
      });

      service.logout();
    });
  });
});
