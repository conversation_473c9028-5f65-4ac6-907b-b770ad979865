.header-toolbar {
  background-color: #4D4D4D;
  color: white;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: none;
  border: none;
}

.header-content {
  width: 100%;
  max-width: 932px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-button {
  background: transparent;
  border: none;
  padding: 0;
  min-width: auto;
  box-shadow: none;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }
}

.figma-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;

  .arrow-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }

  .logo-icon {
    height: 32.65px;
    width: 100px;
  }
}

.user-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;

  .user-icon {
    color: white;
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .username {
    color: white;
    font-family: 'Nunito', sans-serif;
    font-weight: 700;
    font-size: 12px;
    line-height: 1.364;
  }
}

.separator {
  width: 1px;
  height: 16px;
  background-color: white;
}

.logout-button {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 8px;
  background: transparent;
  border: none;
  padding: 0;
  min-width: auto;
  color: white;

  .logout-text {
    color: white;
    font-family: 'Nunito', sans-serif;
    font-weight: 700;
    font-size: 12px;
    line-height: 1.364;
    order: 1;
  }

  .logout-icon {
    color: white;
    font-size: 24px;
    width: 24px;
    height: 24px;
    order: 2;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .user-info .username {
    display: none;
  }
  
  .logout-button span {
    display: none;
  }
}
