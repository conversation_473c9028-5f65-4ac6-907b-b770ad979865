.airport-card {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  width: 100%;
  max-width: 700px;
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;

  &:hover {
    box-shadow: 2px 2px 8px 0px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }
}

.airport-name {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  
  .airport-description {
    font-family: 'Nunito', sans-serif;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.364;
    color: #4D4D4D;
    margin-bottom: 4px;
  }
  
  .city-code {
    font-family: 'Nunito', sans-serif;
    font-weight: 700;
    font-size: 12px;
    line-height: 1.364;
    color: #CCCCCC;
  }
}

.airport-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  
  .detail-label {
    font-family: 'Nunito', sans-serif;
    font-weight: 700;
    font-size: 12px;
    line-height: 1.364;
    color: #4D4D4D;
  }
}

// Responsive design
@media (max-width: 768px) {
  .airport-card {
    padding: 12px;
  }
  
  .airport-name .airport-description {
    font-size: 14px;
  }
}
