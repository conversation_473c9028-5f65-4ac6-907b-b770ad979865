<mat-toolbar class="header-toolbar">
  <div class="header-content">
    <!-- Logo -->
    <div class="logo-section">
      <div class="figma-logo" (click)="navigateToHome()">
        <img src="assets/images/arrow-left.svg" alt="Arrow" class="arrow-icon">
        <img src="assets/images/logo.svg" alt="Airports World Logo" class="logo-icon">
      </div>
    </div>

    <!-- Usuario y logout -->
    <div class="user-section" *ngIf="currentUser$ | async as user">
      <div class="user-info">
        <mat-icon class="user-icon">account_circle</mat-icon>
        <span class="username">{{ user.username }}</span>
      </div>

      <div class="separator"></div>
      <button mat-button class="logout-button" (click)="onLogout()">
        <span class="logout-text">Logout</span>
        <mat-icon class="logout-icon">logout</mat-icon>
      </button>
    </div>
  </div>
</mat-toolbar>