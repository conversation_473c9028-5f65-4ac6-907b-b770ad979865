{"name": "airports-of-the-world", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "stubs": "stubby -s 1500 -l 127.0.0.1 -d stubbs/stubby.json -w", "dev": "concurrently \"npm run stubs\" \"npm run start\""}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/elements": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/material": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.6.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@rollup/rollup-win32-x64-msvc": "^4.41.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.0.0", "concurrently": "^8.2.0", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "stubby": "^5.1.0", "typescript": "~5.5.0"}}