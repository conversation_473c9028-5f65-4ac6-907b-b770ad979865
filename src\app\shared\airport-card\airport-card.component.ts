import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { Airport } from '../../airports-list/airport';

@Component({
  selector: 'app-airport-card',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule
  ],
  templateUrl: './airport-card.component.html',
  styleUrls: ['./airport-card.component.scss']
})
export class AirportCardComponent {
  @Input() airport!: Airport;
  @Output() cardClick = new EventEmitter<string>();

  onCardClick(): void {
    this.cardClick.emit(this.airport.key);
  }
}
