.airport-detail-container {
  display: flex;
  gap: 32px;
  max-width: 932px;
  margin: 0 auto;
  padding: 32px 254px;
  min-height: calc(100vh - 120px);
}

.sidebar {
  flex-shrink: 0;
  width: 200px;
}

.content {
  flex: 1;
  max-width: 700px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    font-family: 'Nunito', sans-serif;
    font-size: 14px;
    color: #666;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;

  .error-icon {
    font-size: 48px;
    color: #f44336;
    margin-bottom: 16px;
  }

  .error-message {
    font-family: 'Nunito', sans-serif;
    font-size: 16px;
    color: #666;
    margin-bottom: 24px;
  }

  .error-actions {
    display: flex;
    gap: 16px;
  }
}

.airport-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.airport-title {
  font-family: 'Nunito', sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.364;
  color: #333333;
  margin: 0;
}

.airport-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;

  .img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.airport-description {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .paragraph {
    font-family: 'Nunito', sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 1.5;
    color: #333333;
    margin: 0;
    text-align: justify;
  }
}

.airport-card {
  background: white;
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;

  .airport-name {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .airport-description-item {
      font-family: 'Nunito', sans-serif;
      font-weight: 700;
      font-size: 16px;
      line-height: 1.364;
      color: #333333;
    }

    .city-code {
      font-family: 'Nunito', sans-serif;
      font-weight: 700;
      font-size: 12px;
      line-height: 1.364;
      color: #CCCCCC;
    }
  }

  .city, .country {
    .city-description, .country-description {
      font-family: 'Nunito', sans-serif;
      font-weight: 700;
      font-size: 12px;
      line-height: 1.364;
      color: #333333;
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .airport-detail-container {
    padding: 32px 32px;
  }
}

@media (max-width: 768px) {
  .airport-detail-container {
    flex-direction: column;
    padding: 0;
    gap: 0;
    overflow-x: hidden;
  }

  .sidebar {
    order: -1;
    width: 100%;
    margin: 0;
  }

  .content {
    max-width: 100%;
    padding: 16px 16px 16px 0;
    margin-top: 16px;
    margin-left: 16px;
  }

  .airport-title {
    font-size: 20px;
  }

  .airport-image {
    height: 150px;
  }
}
