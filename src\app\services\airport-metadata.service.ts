import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AirportMetadataService {
  
  private readonly airportImages: { [key: string]: string } = {
    'BCN': 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=700&h=200&fit=crop',
    'MAD': 'https://images.unsplash.com/photo-1583531172005-814191b8b6c0?w=700&h=200&fit=crop',
    'VLC': 'https://images.unsplash.com/photo-1583532452513-a02186875cdb?w=700&h=200&fit=crop',
    'SVQ': 'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=700&h=200&fit=crop',
    'BIO': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=700&h=200&fit=crop',
    'LCG': 'https://images.unsplash.com/photo-1505761671935-60b3a7427bad?w=700&h=200&fit=crop'
  };

  private readonly airportDescriptions: { [key: string]: { title: string, desc1: string, desc2: string } } = {
    'BCN': {
      title: 'Aeropuerto de Barcelona - El Prat',
      desc1: 'El Aeropuerto de Barcelona-El Prat es el segundo aeropuerto más importante de España y una de las principales puertas de entrada al país. Situado a tan solo 12 kilómetros del centro de Barcelona, este moderno aeropuerto conecta la ciudad condal con más de 180 destinos en todo el mundo.',
      desc2: 'Con sus dos terminales modernas y su excelente conectividad tanto nacional como internacional, el aeropuerto ofrece una experiencia excepcional a los viajeros. Sus instalaciones incluyen una amplia variedad de tiendas, restaurantes y servicios que reflejan la rica cultura catalana.'
    },
    'MAD': {
      title: 'Aeropuerto Adolfo Suárez Madrid-Barajas',
      desc1: 'El Aeropuerto Adolfo Suárez Madrid-Barajas es el principal aeropuerto de España y uno de los más importantes de Europa. Ubicado en la capital española, sirve como el hub principal de Iberia y conecta Madrid con destinos en todos los continentes.',
      desc2: 'Su Terminal 4, diseñada por Richard Rogers, es considerada una obra maestra de la arquitectura aeroportuaria moderna. Con su impresionante diseño y tecnología de vanguardia, ofrece una experiencia única a millones de pasajeros cada año.'
    },
    'VLC': {
      title: 'Aeropuerto de Valencia',
      desc1: 'El Aeropuerto de Valencia es la puerta de entrada principal a la Comunidad Valenciana, una región conocida por sus playas mediterráneas, su rica gastronomía y su patrimonio histórico. Ubicado estratégicamente en la costa este de España, conecta la ciudad con principales destinos europeos.',
      desc2: 'Este moderno aeropuerto ha experimentado un crecimiento significativo en los últimos años, consolidándose como un punto clave para el turismo mediterráneo. Sus instalaciones renovadas ofrecen comodidad y eficiencia a todos los viajeros.'
    },
    'SVQ': {
      title: 'Aeropuerto de Sevilla',
      desc1: 'El Aeropuerto de Sevilla San Pablo es la principal puerta de entrada a Andalucía occidental, una región rica en historia, cultura y tradiciones. Situado en el corazón de Andalucía, este aeropuerto conecta la hermosa ciudad de Sevilla con destinos nacionales e internacionales.',
      desc2: 'Con su arquitectura que refleja el estilo andaluz y sus modernas instalaciones, el aeropuerto ofrece una cálida bienvenida a los visitantes que llegan para descubrir los encantos de Sevilla, desde la majestuosa Catedral hasta los pintorescos barrios del Casco Antiguo.'
    },
    'BIO': {
      title: 'Aeropuerto de Bilbao',
      desc1: 'El Aeropuerto de Bilbao es la principal puerta de entrada al País Vasco, una región única con su propia identidad cultural, gastronómica y lingüística. Conecta la vibrante ciudad de Bilbao y sus alrededores con importantes destinos europeos.',
      desc2: 'Conocido por su eficiencia y diseño moderno, el aeropuerto sirve tanto a viajeros de negocios como turistas que vienen a descubrir el renacimiento cultural de Bilbao, hogar del famoso Museo Guggenheim y una escena culinaria de clase mundial.'
    },
    'LCG': {
      title: 'Aeropuerto de A Coruña',
      desc1: 'El Aeropuerto de A Coruña es la principal conexión aérea de Galicia con el resto del mundo. Situado en el noroeste de España, sirve como puerta de entrada a una región conocida por sus paisajes verdes, su rica tradición marinera y su patrimonio celta.',
      desc2: 'Este aeropuerto conecta A Coruña con destinos nacionales e internacionales, facilitando el acceso a las Rías Altas, el Camino de Santiago y la vibrante cultura gallega. Sus instalaciones modernas proporcionan una experiencia cómoda para todos los viajeros.'
    }
  };

  private readonly defaultImage = 'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=700&h=200&fit=crop';
  private readonly defaultDescription = {
    title: 'Aeropuerto',
    desc1: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eu turpis molestie, dictum est a, mattis tellus.',
    desc2: 'Curabitur tempor quis eros tempus lacinia. Nam bibendum pellentesque quam a convallis.'
  };

  constructor() { }

  /**
   * Obtiene la URL de la imagen para un aeropuerto específico
   * @param key Código del aeropuerto
   * @returns URL de la imagen o imagen por defecto
   */
  getAirportImage(key: string): string {
    return this.airportImages[key] || this.defaultImage;
  }

  /**
   * Obtiene la descripción completa para un aeropuerto específico
   * @param key Código del aeropuerto
   * @returns Objeto con título y descripciones o valores por defecto
   */
  getAirportDescription(key: string): { title: string, desc1: string, desc2: string } {
    return this.airportDescriptions[key] || this.defaultDescription;
  }

  /**
   * Obtiene el título del aeropuerto
   * @param key Código del aeropuerto
   * @returns Título del aeropuerto
   */
  getAirportTitle(key: string): string {
    const description = this.getAirportDescription(key);
    return description.title;
  }

  /**
   * Obtiene la primera descripción del aeropuerto
   * @param key Código del aeropuerto
   * @returns Primera descripción del aeropuerto
   */
  getAirportDescription1(key: string): string {
    const description = this.getAirportDescription(key);
    return description.desc1;
  }

  /**
   * Obtiene la segunda descripción del aeropuerto
   * @param key Código del aeropuerto
   * @returns Segunda descripción del aeropuerto
   */
  getAirportDescription2(key: string): string {
    const description = this.getAirportDescription(key);
    return description.desc2;
  }
}
