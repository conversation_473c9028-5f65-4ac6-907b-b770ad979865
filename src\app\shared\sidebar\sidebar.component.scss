.sidebar-container {
  width: 200px;
  background: white;
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.menu-items {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #DDDDDD;
  cursor: pointer;
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f5f5;
  }

  .menu-text {
    font-family: 'Nunito', sans-serif;
    font-weight: 700;
    font-size: 14px;
    line-height: 1.364;
    color: #333333;
    flex: 1;
    transition: color 0.2s ease;
  }

  .chevron-icon {
    color: #333333;
    font-size: 24px;
    width: 24px;
    height: 24px;
    transition: color 0.2s ease;
  }

  &.home-item {
    background-color: #f9f9f9;
    
    &:hover {
      background-color: #eeeeee;
    }

    .menu-text {
      font-weight: 800;
      color: #2196F3;
    }

    .chevron-icon {
      color: #2196F3;
    }

    &.active {
      background-color: #FFCC00;
      border-left: 4px solid #FF6600;
      
      &:hover {
        background-color: #FFD700;
      }

      .menu-text {
        color: #4D4D4D;
        font-weight: 800;
      }

      .chevron-icon {
        color: #4D4D4D;
      }
    }
  }

  &.active {
    background-color: #FFCC00;
    border-left: 4px solid #FF6600;
    
    &:hover {
      background-color: #FFD700;
    }

    .menu-text {
      color: #4D4D4D;
      font-weight: 800;
    }

    .chevron-icon {
      color: #4D4D4D;
    }
  }
}

// Mobile responsive styles
@media (max-width: 768px) {
  .sidebar-container {
    width: 100%; // Change from 100vw to 100% to respect parent container
    border-radius: 0; // Remove border radius for edge-to-edge
    border-left: none; // Remove left border
    border-right: none; // Remove right border
    overflow-x: auto;
    overflow-y: hidden;
    margin: 16px 0 0 16px; // Add left margin for mobile spacing
    
    // Custom scrollbar styles
    &::-webkit-scrollbar {
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #FFCC00;
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #E6B800;
    }
  }

  .menu-items {
    flex-direction: row;
    width: calc(100% + 200px); // Extend beyond container to ensure scroll is visible
    gap: 0;
    padding-left: 0; // Remove padding since we now have margin on container
    padding-right: 40px; // More padding to ensure last item is fully visible when scrolled
  }

  .menu-item {
    min-width: 180px;
    width: auto;
    white-space: nowrap;
    border-bottom: none;
    border-right: 1px solid #DDDDDD;
    flex-shrink: 0;

    &:last-child {
      border-right: none;
      margin-right: 40px; // Extra margin for the last item to indicate more content
    }

    .menu-text {
      font-size: 13px;
      padding-right: 8px;
    }

    .chevron-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    // Active states in mobile - more prominent
    &.active {
      border-left: none;
      border-bottom: 4px solid #FF6600;
      background-color: #FFCC00 !important; // Ensure active background is visible
      
      .menu-text {
        color: #4D4D4D !important;
        font-weight: 800 !important;
      }

      .chevron-icon {
        color: #4D4D4D !important;
      }
    }

    &.home-item.active {
      border-left: none;
      border-bottom: 4px solid #FF6600;
      background-color: #FFCC00 !important; // Ensure active background is visible
      
      .menu-text {
        color: #4D4D4D !important;
        font-weight: 800 !important;
      }

      .chevron-icon {
        color: #4D4D4D !important;
      }
    }
  }
}
