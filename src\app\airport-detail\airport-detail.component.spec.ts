import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError, Subject } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { AirportDetailComponent } from './airport-detail.component';
import { AirportsListService } from '../airports-list/airports-list.service';
import { AirportMetadataService } from '../services/airport-metadata.service';
import { Airport } from '../airports-list/airport';

describe('AirportDetailComponent', () => {
  let component: AirportDetailComponent;
  let fixture: ComponentFixture<AirportDetailComponent>;
  let mockAirportsService: jasmine.SpyObj<AirportsListService>;
  let mockMetadataService: jasmine.SpyObj<AirportMetadataService>;
  let mockActivatedRoute: any;
  let mockRouter: jasmine.SpyObj<Router>;

  const mockAirport: Airport = {
    key: 'BCN',
    name: 'Barcelona Airport',
    country: 'Spain',
    city: 'Barcelona'
  };

  beforeEach(async () => {
    const airportsServiceSpy = jasmine.createSpyObj('AirportsListService', ['getAirport']);
    const metadataServiceSpy = jasmine.createSpyObj('AirportMetadataService', [
      'getAirportTitle',
      'getAirportImage',
      'getAirportDescription1',
      'getAirportDescription2'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    mockActivatedRoute = {
      params: of({ id: 'BCN' })
    };

    await TestBed.configureTestingModule({
      imports: [AirportDetailComponent, NoopAnimationsModule],
      providers: [
        { provide: AirportsListService, useValue: airportsServiceSpy },
        { provide: AirportMetadataService, useValue: metadataServiceSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: routerSpy }
      ]
    })
    .compileComponents();

    mockAirportsService = TestBed.inject(AirportsListService) as jasmine.SpyObj<AirportsListService>;
    mockMetadataService = TestBed.inject(AirportMetadataService) as jasmine.SpyObj<AirportMetadataService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    fixture = TestBed.createComponent(AirportDetailComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Route parameter handling', () => {
    it('should extract airport ID from route parameters', () => {
      mockAirportsService.getAirport.and.returnValue(of(mockAirport));

      fixture.detectChanges();

      expect(component.airportKey).toBe('BCN');
      expect(mockAirportsService.getAirport).toHaveBeenCalledWith('BCN');
    });

    it('should not load airport data if no ID in route', () => {
      mockActivatedRoute.params = of({});

      fixture.detectChanges();

      expect(component.airportKey).toBeUndefined();
      expect(mockAirportsService.getAirport).not.toHaveBeenCalled();
    });
  });

  describe('Data loading', () => {
    it('should load airport data successfully', () => {
      mockAirportsService.getAirport.and.returnValue(of(mockAirport));

      fixture.detectChanges();

      expect(component.airport).toEqual(mockAirport);
      expect(component.loading).toBeFalsy();
      expect(component.error).toBeUndefined();
    });

    it('should set loading state during data fetch', () => {
      const subject = new Subject<Airport>();
      mockAirportsService.getAirport.and.returnValue(subject.asObservable());

      fixture.detectChanges();

      expect(component.loading).toBeTruthy();
      expect(component.error).toBeUndefined();

      subject.next(mockAirport);
      subject.complete();

      expect(component.loading).toBeFalsy();
      expect(component.airport).toEqual(mockAirport);
    });

    it('should handle error state', () => {
      const errorMessage = 'Airport not found';
      mockAirportsService.getAirport.and.returnValue(throwError(() => new Error(errorMessage)));

      fixture.detectChanges();

      expect(component.error).toBe(errorMessage);
      expect(component.loading).toBeFalsy();
      expect(component.airport).toBeUndefined();
    });

    it('should handle error with default message', () => {
      mockAirportsService.getAirport.and.returnValue(throwError(() => ({})));

      fixture.detectChanges();

      expect(component.error).toBe('Error al cargar el detalle del aeropuerto');
      expect(component.loading).toBeFalsy();
    });
  });

  describe('Metadata methods', () => {
    beforeEach(() => {
      component.airport = mockAirport;
      mockMetadataService.getAirportTitle.and.returnValue('Barcelona - El Prat');
      mockMetadataService.getAirportImage.and.returnValue('https://example.com/bcn.jpg');
      mockMetadataService.getAirportDescription1.and.returnValue('Description 1');
      mockMetadataService.getAirportDescription2.and.returnValue('Description 2');
    });

    it('should get airport display name', () => {
      const result = component.getAirportDisplayName();

      expect(result).toBe('Barcelona - El Prat');
      expect(mockMetadataService.getAirportTitle).toHaveBeenCalledWith('BCN');
    });

    it('should return empty string for display name when no airport', () => {
      component.airport = undefined;

      const result = component.getAirportDisplayName();

      expect(result).toBe('');
      expect(mockMetadataService.getAirportTitle).not.toHaveBeenCalled();
    });

    it('should get airport image', () => {
      const result = component.getAirportImage();

      expect(result).toBe('https://example.com/bcn.jpg');
      expect(mockMetadataService.getAirportImage).toHaveBeenCalledWith('BCN');
    });

    it('should return empty string for image when no airport', () => {
      component.airport = undefined;

      const result = component.getAirportImage();

      expect(result).toBe('');
      expect(mockMetadataService.getAirportImage).not.toHaveBeenCalled();
    });

    it('should get airport description 1', () => {
      const result = component.getAirportDescription1();

      expect(result).toBe('Description 1');
      expect(mockMetadataService.getAirportDescription1).toHaveBeenCalledWith('BCN');
    });

    it('should get airport description 2', () => {
      const result = component.getAirportDescription2();

      expect(result).toBe('Description 2');
      expect(mockMetadataService.getAirportDescription2).toHaveBeenCalledWith('BCN');
    });
  });

  describe('Navigation and user interactions', () => {
    it('should navigate back to airports list', () => {
      component.goBack();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/airports']);
    });

    it('should retry loading airport data', () => {
      component.airportKey = 'BCN';
      mockAirportsService.getAirport.and.returnValue(of(mockAirport));

      component.retry();

      expect(mockAirportsService.getAirport).toHaveBeenCalledWith('BCN');
    });

    it('should not retry if no airport key', () => {
      component.airportKey = undefined;

      component.retry();

      expect(mockAirportsService.getAirport).not.toHaveBeenCalled();
    });
  });

  describe('Component lifecycle', () => {
    it('should unsubscribe on destroy', () => {
      spyOn(component['destroy$'], 'next');
      spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(component['destroy$'].next).toHaveBeenCalled();
      expect(component['destroy$'].complete).toHaveBeenCalled();
    });
  });
});