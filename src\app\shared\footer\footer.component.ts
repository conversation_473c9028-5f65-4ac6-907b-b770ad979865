import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [MatIconModule, MatButtonModule],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss'
})
export class FooterComponent {
  currentYear = new Date().getFullYear();

  constructor(private router: Router) {}

  onLinkClick(link: string, event?: Event): void {
    // Prevenir el comportamiento por defecto del enlace
    event?.preventDefault();
    
    switch (link) {
      case 'home':
        this.router.navigate(['/']);
        break;
      case 'airports':
        this.router.navigate(['/airports']);
        break;
      case 'about':
        console.log('Navegando a Acerca de');
        break;
      case 'privacy':
        console.log('Navegando a Política de Privacidad');
        break;
      case 'terms':
        console.log('Navegando a Términos de Uso');
        break;
      case 'cookies':
        console.log('Navegando a Política de Cookies');
        break;
      default:
        console.log(`Enlace no implementado: ${link}`);
    }
  }

  onSocialClick(social: string): void {
    const socialUrls = {
      facebook: 'https://facebook.com',
      twitter: 'https://twitter.com',
      instagram: 'https://instagram.com',
      linkedin: 'https://linkedin.com'
    };
    
    const url = socialUrls[social as keyof typeof socialUrls];
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  }
}
