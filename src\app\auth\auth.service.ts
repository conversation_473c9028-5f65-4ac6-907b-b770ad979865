import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface User {
  username: string;
  securityKey: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    // Verificar si hay una sesión guardada en localStorage
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      try {
        this.currentUserSubject.next(JSON.parse(savedUser));
      } catch (error) {
        console.warn('Invalid user data in localStorage, clearing...', error);
        localStorage.removeItem('currentUser');
      }
    }
  }

  login(username: string, securityKey: string): boolean {
    // Validación simple - cualquier valor es válido según los requisitos
    if (username && securityKey) {
      const user: User = { username, securityKey };
      try {
        localStorage.setItem('currentUser', JSON.stringify(user));
        this.currentUserSubject.next(user);
        return true;
      } catch (error) {
        console.error('Failed to save user data to localStorage:', error);
        // Aún actualizamos el subject para mantener la sesión en memoria
        this.currentUserSubject.next(user);
        return true;
      }
    }
    return false;
  }

  logout(): void {
    try {
      localStorage.removeItem('currentUser');
    } catch (error) {
      console.error('Failed to remove user data from localStorage:', error);
    }
    this.currentUserSubject.next(null);
  }

  isLoggedIn(): boolean {
    return this.currentUserSubject.value !== null;
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  getSecurityKey(): string | null {
    const user = this.getCurrentUser();
    return user ? user.securityKey : null;
  }
}
