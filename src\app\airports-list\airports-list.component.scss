@import "../../styles";

.header {
  background: $gray-75;
  color: $color-white;
  padding: 20px;
}

mat-toolbar {
  background: $color-primary-a;
}

.airports-container {
  display: flex;
  gap: 32px;
  max-width: 932px;
  margin: 0 auto;
  padding: 32px 254px;
  min-height: calc(100vh - 120px);
}

.sidebar {
  flex-shrink: 0;
  width: 200px;
}

.content {
  flex: 1;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page-title {
  font-family: 'Nunito', sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 1.364;
  color: #333333;
  margin: 0;
}

.main-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;

  .img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.page-description {
  margin-bottom: 16px;

  .description-text {
    font-family: 'Nunito', sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 1.5;
    color: #333333;
    margin: 0;
    text-align: justify;
  }
}

.airports-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.airports-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  
  p {
    font-family: 'Nunito', sans-serif;
    font-size: 14px;
    color: #666666;
    margin: 0;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ffcdd2;
  
  .error-message {
    font-family: 'Nunito', sans-serif;
    font-size: 14px;
    color: #f44336;
    margin: 0;
    text-align: center;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .airports-container {
    padding: 32px 32px;
  }
}

@media (max-width: 768px) {
  .airports-container {
    flex-direction: column;
    padding: 0;
    gap: 0;
    overflow-x: hidden;
  }

  .sidebar {
    order: -1;
    width: 100%;
    margin: 0;
  }

  .content {
    max-width: 100%;
    padding: 16px 16px 16px 0;
    margin-top: 16px;
    margin-left: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .main-image {
    height: 150px;
  }
}



