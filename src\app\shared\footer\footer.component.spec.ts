import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { FooterComponent } from './footer.component';

describe('FooterComponent', () => {
  let component: FooterComponent;
  let fixture: ComponentFixture<FooterComponent>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [FooterComponent],
      providers: [
        { provide: Router, useValue: routerSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FooterComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display current year', () => {
    expect(component.currentYear).toBe(new Date().getFullYear());
  });

  it('should navigate to home when clicking home link', () => {
    component.onLinkClick('home');
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/']);
  });

  it('should navigate to airports when clicking airports link', () => {
    component.onLinkClick('airports');
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/airports']);
  });

  it('should open social media links', () => {
    spyOn(window, 'open');
    component.onSocialClick('facebook');
    expect(window.open).toHaveBeenCalledWith('https://facebook.com', '_blank', 'noopener,noreferrer');
  });
});
