{"version": 3, "sources": ["src/app/shared/header/header.component.scss"], "sourcesContent": [".header-toolbar {\r\n  background-color: #4D4D4D;\r\n  color: white;\r\n  height: 64px;\r\n  padding: 0;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  max-width: 932px;\r\n  margin: 0 auto;\r\n  padding: 0 20px;\r\n  height: 100%;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.back-button {\r\n  color: white;\r\n  \r\n  mat-icon {\r\n    font-size: 24px;\r\n    width: 24px;\r\n    height: 24px;\r\n  }\r\n}\r\n\r\n.logo {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  \r\n  .logo-text {\r\n    color: white;\r\n  }\r\n  \r\n  .logo-accent {\r\n    color: #FFCC00;\r\n    margin-left: 4px;\r\n  }\r\n}\r\n\r\n.user-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  \r\n  .user-icon {\r\n    font-size: 24px;\r\n    width: 24px;\r\n    height: 24px;\r\n    color: white;\r\n  }\r\n  \r\n  .username {\r\n    font-size: 12px;\r\n    font-weight: 700;\r\n    color: white;\r\n  }\r\n}\r\n\r\n.separator {\r\n  width: 1px;\r\n  height: 16px;\r\n  background-color: white;\r\n}\r\n\r\n.logout-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: white;\r\n  font-size: 12px;\r\n  font-weight: 700;\r\n  \r\n  &:hover {\r\n    background-color: rgba(255, 255, 255, 0.1);\r\n  }\r\n  \r\n  mat-icon {\r\n    font-size: 18px;\r\n    width: 18px;\r\n    height: 18px;\r\n  }\r\n}\r\n\r\n// Responsive design\r\n@media (max-width: 768px) {\r\n  .header-content {\r\n    padding: 0 16px;\r\n  }\r\n  \r\n  .logo {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .user-info .username {\r\n    display: none;\r\n  }\r\n  \r\n  .logout-button span {\r\n    display: none;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,oBAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA,EAAA;AACA,WAAA,EAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,SAAA;;AAEA,CAHF,YAGE;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;;AAEA,CANF,KAME,CAAA;AACE,SAAA;;AAGF,CAVF,KAUE,CAAA;AACE,SAAA;AACA,eAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,CALF,UAKE,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAGF,CAZF,UAYE,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAIJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,oBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;;AAEA,CARF,aAQE;AACE,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAZF,cAYE;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GA/FF;AAgGI,aAAA,EAAA;;AAGF,GAxEF;AAyEI,eAAA;;AAGF,GAtDF,UAsDE,CA1CA;AA2CE,aAAA;;AAGF,GAjCF,cAiCE;AACE,aAAA;;;", "names": []}