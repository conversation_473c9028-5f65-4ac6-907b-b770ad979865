import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {Router} from '@angular/router';
import {MatToolbarModule} from '@angular/material/toolbar';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {AirportsListService} from './airports-list.service';
import {Airport} from './airport';
import {AirportCardComponent} from '../shared/airport-card/airport-card.component';
import {HeaderComponent} from '../shared/header/header.component';
import {FooterComponent} from '../shared/footer/footer.component';
import {SidebarComponent} from '../shared/sidebar/sidebar.component';

@Component({
    selector: 'app-airports-list',
    standalone: true,
    imports: [
        CommonModule,
        MatToolbarModule,
        MatProgressSpinnerModule,
        AirportCardComponent,
        HeaderComponent,
        FooterComponent,
        SidebarComponent
    ],
    templateUrl: './airports-list.component.html',
    styleUrls: ['./airports-list.component.scss']
})
export class AirportsListComponent implements OnInit{
    public airportsList?: Airport[];
    public error?: string;
    public loading = false;

    constructor(
        private airportsListService: AirportsListService,
        private router: Router
    ) {}

    public ngOnInit(): void {
        this.loadAirports();
    }

    private loadAirports(): void {
        this.loading = true;
        this.error = undefined;
        
        this.airportsListService.getAllAirports().subscribe({
            next: (airports) => {
                this.airportsList = airports;
                this.loading = false;
            },
            error: (error) => {
                this.error = error.message || 'Error al cargar los aeropuertos';
                this.loading = false;
            }
        });
    }

    public onAirportClick(airportKey: string): void {
        this.router.navigate(['/airport', airportKey]);
    }
}
