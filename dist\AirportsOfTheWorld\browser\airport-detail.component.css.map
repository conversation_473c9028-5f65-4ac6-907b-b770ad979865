{"version": 3, "sources": ["src/app/airport-detail/airport-detail.component.scss"], "sourcesContent": [".airport-detail-container {\r\n  min-height: calc(100vh - 64px);\r\n  background-color: #f5f5f5;\r\n  padding: 32px 20px;\r\n}\r\n\r\n.content {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 40px;\r\n  \r\n  p {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 40px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ffcdd2;\r\n  \r\n  .error-icon {\r\n    font-size: 48px;\r\n    width: 48px;\r\n    height: 48px;\r\n    color: #f44336;\r\n  }\r\n  \r\n  .error-message {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-size: 16px;\r\n    color: #f44336;\r\n    margin: 0;\r\n    text-align: center;\r\n  }\r\n  \r\n  .error-actions {\r\n    display: flex;\r\n    gap: 16px;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n\r\n.airport-detail {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24px;\r\n}\r\n\r\n.detail-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  \r\n  .back-button {\r\n    color: #4D4D4D;\r\n  }\r\n  \r\n  .airport-title {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-weight: 700;\r\n    font-size: 24px;\r\n    line-height: 1.364;\r\n    color: #333333;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n.airport-info-card {\r\n  border-radius: 8px;\r\n  box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.16);\r\n  border: 1px solid #DDDDDD;\r\n  \r\n  .airport-name {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-weight: 700;\r\n    font-size: 20px;\r\n    line-height: 1.364;\r\n    color: #4D4D4D;\r\n  }\r\n  \r\n  .airport-code {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-weight: 700;\r\n    font-size: 14px;\r\n    line-height: 1.364;\r\n    color: #CCCCCC;\r\n  }\r\n}\r\n\r\n.airport-details {\r\n  margin-top: 16px;\r\n}\r\n\r\n.detail-section {\r\n  .section-title {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-weight: 700;\r\n    font-size: 18px;\r\n    line-height: 1.364;\r\n    color: #333333;\r\n    margin: 0 0 16px 0;\r\n  }\r\n}\r\n\r\n.detail-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr;\r\n  gap: 16px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 16px;\r\n  background: #f9f9f9;\r\n  border-radius: 8px;\r\n  border: 1px solid #e0e0e0;\r\n  \r\n  .detail-icon {\r\n    color: #4D4D4D;\r\n    font-size: 24px;\r\n    width: 24px;\r\n    height: 24px;\r\n  }\r\n  \r\n  .detail-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n    \r\n    .detail-label {\r\n      font-family: 'Nunito', sans-serif;\r\n      font-weight: 700;\r\n      font-size: 12px;\r\n      line-height: 1.364;\r\n      color: #666666;\r\n      text-transform: uppercase;\r\n    }\r\n    \r\n    .detail-value {\r\n      font-family: 'Nunito', sans-serif;\r\n      font-weight: 700;\r\n      font-size: 16px;\r\n      line-height: 1.364;\r\n      color: #4D4D4D;\r\n    }\r\n  }\r\n}\r\n\r\nmat-card-actions {\r\n  padding: 16px 24px;\r\n  \r\n  button {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n// Responsive design\r\n@media (max-width: 768px) {\r\n  .airport-detail-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .detail-header {\r\n    .airport-title {\r\n      font-size: 20px;\r\n    }\r\n  }\r\n  \r\n  .detail-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,cAAA,KAAA,MAAA,EAAA;AACA,oBAAA;AACA,WAAA,KAAA;;AAGF,CAAA;AACE,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;;AAEA,CAPF,kBAOE;AACE,eAAA,QAAA,EAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CAVF,gBAUE,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAGF,CAjBF,gBAiBE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;;AAGF,CAzBF,gBAyBE,CAAA;AACE,WAAA;AACA,OAAA;AACA,cAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,CALF,cAKE,CAAA;AACE,SAAA;;AAGF,CATF,cASE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,iBAAA;AACA,cAAA,IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CALF,kBAKE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAbF,kBAaE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAIJ,CAAA;AACE,cAAA;;AAIA,CAAA,eAAA,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA,EAAA,EAAA,KAAA;;AAIJ,CAAA;AACE,WAAA;AACA,yBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CATF,YASE,CAAA;AACE,SAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAhBF,YAgBE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAEA,CArBJ,YAqBI,CALF,eAKE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,kBAAA;;AAGF,CA9BJ,YA8BI,CAdF,eAcE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAKN;AACE,WAAA,KAAA;;AAEA,iBAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GAlLF;AAmLI,aAAA;;AAIA,GAvHJ,cAuHI,CA9GF;AA+GI,eAAA;;AAIJ,GApEF;AAqEI,2BAAA;;;", "names": []}