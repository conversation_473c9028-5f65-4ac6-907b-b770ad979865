{"version": 3, "sources": ["src/app/shared/footer/footer.component.scss"], "sourcesContent": [".footer-container {\r\n  background-color: #4D4D4D;\r\n  color: white;\r\n  margin-top: auto;\r\n  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.footer-content {\r\n  max-width: 932px;\r\n  margin: 0 auto;\r\n  padding: 40px 20px;\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr 1fr;\r\n  gap: 40px;\r\n  align-items: start;\r\n}\r\n\r\n.footer-main {\r\n  .footer-logo {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    margin-bottom: 16px;\r\n    \r\n    .logo-text {\r\n      color: white;\r\n    }\r\n    \r\n    .logo-accent {\r\n      color: #FFCC00;\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n  \r\n  .footer-description {\r\n    p {\r\n      font-size: 14px;\r\n      line-height: 1.5;\r\n      color: rgba(255, 255, 255, 0.8);\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.footer-links {\r\n  display: flex;\r\n  gap: 32px;\r\n  \r\n  .links-section {\r\n    h4 {\r\n      font-size: 16px;\r\n      font-weight: 700;\r\n      color: white;\r\n      margin: 0 0 16px 0;\r\n    }\r\n    \r\n    ul {\r\n      list-style: none;\r\n      padding: 0;\r\n      margin: 0;\r\n      \r\n      li {\r\n        margin-bottom: 8px;\r\n        \r\n        a {\r\n          color: rgba(255, 255, 255, 0.8);\r\n          text-decoration: none;\r\n          font-size: 14px;\r\n          transition: color 0.3s ease;\r\n          cursor: pointer;\r\n          \r\n          &:hover {\r\n            color: #FFCC00;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.footer-social {\r\n  h4 {\r\n    font-size: 16px;\r\n    font-weight: 700;\r\n    color: white;\r\n    margin: 0 0 16px 0;\r\n  }\r\n  \r\n  .social-icons {\r\n    display: flex;\r\n    gap: 8px;\r\n    \r\n    .social-button {\r\n      color: rgba(255, 255, 255, 0.8);\r\n      transition: all 0.3s ease;\r\n      \r\n      &:hover {\r\n        color: #FFCC00;\r\n        background-color: rgba(255, 204, 0, 0.1);\r\n      }\r\n      \r\n      mat-icon {\r\n        font-size: 20px;\r\n        width: 20px;\r\n        height: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.footer-bottom {\r\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  \r\n  .footer-copyright {\r\n    max-width: 932px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n    text-align: center;\r\n    \r\n    p {\r\n      font-size: 12px;\r\n      color: rgba(255, 255, 255, 0.6);\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// Responsive design\r\n@media (max-width: 768px) {\r\n  .footer-content {\r\n    grid-template-columns: 1fr;\r\n    gap: 24px;\r\n    padding: 32px 16px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .footer-links {\r\n    justify-content: center;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .footer-social {\r\n    .social-icons {\r\n      justify-content: center;\r\n    }\r\n  }\r\n  \r\n  .footer-copyright {\r\n    padding: 16px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .footer-links {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .footer-main .footer-logo {\r\n    font-size: 20px;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,oBAAA;AACA,SAAA;AACA,cAAA;AACA,cAAA,EAAA,KAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,aAAA;AACA,UAAA,EAAA;AACA,WAAA,KAAA;AACA,WAAA;AACA,yBAAA,IAAA,IAAA;AACA,OAAA;AACA,eAAA;;AAIA,CAAA,YAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;;AAEA,CAPF,YAOE,CAPF,YAOE,CAAA;AACE,SAAA;;AAGF,CAXF,YAWE,CAXF,YAWE,CAAA;AACE,SAAA;AACA,eAAA;;AAKF,CAlBF,YAkBE,CAAA,mBAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;;AAKN,CAAA;AACE,WAAA;AACA,OAAA;;AAGE,CALJ,aAKI,CAAA,cAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA,EAAA,EAAA,KAAA;;AAGF,CAZJ,aAYI,CAPA,cAOA;AACE,cAAA;AACA,WAAA;AACA,UAAA;;AAEA,CAjBN,aAiBM,CAZF,cAYE,GAAA;AACE,iBAAA;;AAEA,CApBR,aAoBQ,CAfJ,cAeI,GAAA,GAAA;AACE,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,mBAAA;AACA,aAAA;AACA,cAAA,MAAA,KAAA;AACA,UAAA;;AAEA,CA3BV,aA2BU,CAtBN,cAsBM,GAAA,GAAA,CAAA;AACE,SAAA;;AASV,CAAA,cAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA,EAAA,EAAA,KAAA;;AAGF,CAPA,cAOA,CAAA;AACE,WAAA;AACA,OAAA;;AAEA,CAXF,cAWE,CAJF,aAIE,CAAA;AACE,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAfJ,cAeI,CARJ,aAQI,CAJF,aAIE;AACE,SAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,CAAA,EAAA;;AAGF,CApBJ,cAoBI,CAbJ,aAaI,CATF,cASE;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAMR,CAAA;AACE,cAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAJF,cAIE,CAAA;AACE,aAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,cAAA;;AAEA,CAVJ,cAUI,CANF,iBAME;AACE,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;;AAMN,OAAA,CAAA,SAAA,EAAA;AACE,GA5HF;AA6HI,2BAAA;AACA,SAAA;AACA,aAAA,KAAA;AACA,gBAAA;;AAGF,GA7FF;AA8FI,qBAAA;AACA,SAAA;;AAIA,GA9DF,cA8DE,CAvDF;AAwDI,qBAAA;;AAIJ,GAlCA;AAmCE,aAAA;;;AAIJ,OAAA,CAAA,SAAA,EAAA;AACE,GA9GF;AA+GI,oBAAA;AACA,SAAA;;AAGF,GA9IA,YA8IA,CA9IA;AA+IE,eAAA;;;", "names": []}