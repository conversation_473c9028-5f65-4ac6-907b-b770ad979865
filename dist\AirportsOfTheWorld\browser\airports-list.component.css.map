{"version": 3, "sources": ["src/styles.scss", "src/app/airports-list/airports-list.component.scss"], "sourcesContent": ["// Colores del diseño\r\n$color-primary-a: #FFCC00;\r\n$color-primary-b: #eae0c3;\r\n\r\n$color-white: #FFF;\r\n$gray-10: #F7F7F7;\r\n$gray-15: #F3F3F3;\r\n$gray-18: #E9E9E9;\r\n$gray-20: #DDD; //color-primary-b--light\r\n$gray-30: #CCC;\r\n$gray-45: #999;\r\n$gray-60: #4D4D4D; //color-primary-b\r\n$gray-75: #333; // color-primary-b--dark\r\n$color-black: #000;\r\n\r\n// Estilos globales\r\nhtml, body { \r\n  height: 100%; \r\n  background: $gray-10;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\nbody { \r\n  font-family: '<PERSON>uni<PERSON>', 'Roboto', \"Helvetica Neue\", sans-serif;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  color: $gray-75;\r\n}\r\n\r\n// Reset de márgenes para elementos comunes\r\nh1, h2, h3, h4, h5, h6, p {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n// Estilos para Material Design\r\n.mat-mdc-card {\r\n  border-radius: 8px !important;\r\n}\r\n\r\n.mat-mdc-button {\r\n  font-family: 'Nunito', sans-serif !important;\r\n  font-weight: 700 !important;\r\n}\r\n\r\n.mat-mdc-form-field {\r\n  font-family: 'Nunito', sans-serif !important;\r\n}\r\n\r\n// Utilidades\r\n.full-width {\r\n  width: 100%;\r\n}\r\n\r\n.text-center {\r\n  text-align: center;\r\n}\r\n\r\n.flex-center {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n// Responsive breakpoints\r\n$mobile: 480px;\r\n$tablet: 768px;\r\n$desktop: 1024px;\r\n", "@import \"../../styles\";\r\n\r\n.header {\r\n  background: $gray-75;\r\n  color: $color-white;\r\n  padding: 20px;\r\n}\r\n\r\nmat-toolbar {\r\n  background: $color-primary-a;\r\n}\r\n\r\n.aiports-list {\r\n  margin: 10px;\r\n}\r\n\r\n.example-card {\r\n  max-width: 80%;\r\n  margin: 5px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  cursor: pointer;\r\n  .mat-card-content {\r\n    margin-left: 15px;\r\n  }\r\n}\r\n\r\n.airports-container {\r\n  min-height: calc(100vh - 64px);\r\n  background-color: #f5f5f5;\r\n  padding: 32px 20px;\r\n}\r\n\r\n.content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  gap: 32px;\r\n}\r\n\r\n.page-title {\r\n  font-family: 'Nunito', sans-serif;\r\n  font-weight: 700;\r\n  font-size: 24px;\r\n  line-height: 1.364;\r\n  color: #333333;\r\n  margin: 0 0 32px 0;\r\n  text-align: center;\r\n}\r\n\r\n.airports-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.airports-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  width: 100%;\r\n  max-width: 700px;\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 40px;\r\n  \r\n  p {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 40px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  border: 1px solid #ffcdd2;\r\n  \r\n  .error-message {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-size: 14px;\r\n    color: #f44336;\r\n    margin: 0;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n// Responsive design\r\n@media (max-width: 768px) {\r\n  .airports-container {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .content {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .page-title {\r\n    font-size: 20px;\r\n    margin-bottom: 16px;\r\n  }\r\n}\r\n\r\n\r\n\r\n"], "mappings": ";AAgBA;AAAA;AACE,UAAA;AACA,cAbQ;AAcR,UAAA;AACA,WAAA;;AAGF;AACE;IAAA,QAAA;IAAA,QAAA;IAAA,gBAAA;IAAA;AACA,aAAA;AACA,eAAA;AACA,SAfQ;;AAmBV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACE,UAAA;AACA,WAAA;;AAIF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;;AAGF,CAAA;AACE,eAAA,QAAA,EAAA;;AAIF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;;AC5DF,CAAA;AACE,cDSQ;ACRR,SAAA;AACA,WAAA;;AAGF;AACE,cDRgB;;ACWlB,CAAA;AACE,UAAA;;AAGF,CAAA;AACE,aAAA;AACA,UAAA;AACA,eAAA;AACA,gBAAA;AACA,UAAA;;AACA,CANF,aAME,CAAA;AACE,eAAA;;AAIJ,CAAA;AACE,cAAA,KAAA,MAAA,EAAA;AACA,oBAAA;AACA,WAAA,KAAA;;AAGF,CAAA;AACE,aAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA,EAAA,EAAA,KAAA;AACA,cAAA;;AAGF,CAAA;AACE,QAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;;AAEA,CAPF,kBAOE;AACE,eAAA,QAAA,EAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CAVF,gBAUE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GA3EF;AA4EI,aAAA;;AAGF,GAzEF;AA0EI,oBAAA;AACA,SAAA;;AAGF,GAvEF;AAwEI,eAAA;AACA,mBAAA;;;", "names": []}