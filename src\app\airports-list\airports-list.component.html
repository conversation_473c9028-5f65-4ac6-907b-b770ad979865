<app-header></app-header>

<div class="airports-container">
  <!-- Sidebar -->
  <app-sidebar class="sidebar"></app-sidebar>

  <!-- Content -->
  <div class="content">
    <!-- Title -->
    <h1 class="page-title">Airports of the World</h1>

    <!-- Main Image -->
    <div class="main-image">
      <img src="https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=700&h=200&fit=crop" alt="Airports around the world" class="img">
    </div>

    <!-- Description -->
    <div class="page-description">
      <p class="description-text">
        Discover airports from around the world and explore their unique characteristics, locations, and facilities. Our comprehensive database provides detailed information about international airports, helping you plan your travels and learn about aviation infrastructure worldwide.
      </p>
    </div>

    <!-- Airports Content -->
    <div class="airports-content">
      <!-- Loading spinner -->
      <div class="loading-container" *ngIf="loading">
        <mat-spinner></mat-spinner>
        <p>Cargando aeropuertos...</p>
      </div>

      <!-- Error message -->
      <div class="error-container" *ngIf="error && !loading">
        <p class="error-message">{{ error }}</p>
        <button mat-raised-button color="primary" (click)="ngOnInit()">
          Reintentar
        </button>
      </div>

      <!-- Airports list -->
      <div class="airports-list" *ngIf="airportsList && !loading && !error">
        <app-airport-card 
          *ngFor="let airport of airportsList" 
          [airport]="airport"
          (cardClick)="onAirportClick($event)">
        </app-airport-card>
      </div>
    </div>
  </div>
</div>

<app-footer></app-footer>

