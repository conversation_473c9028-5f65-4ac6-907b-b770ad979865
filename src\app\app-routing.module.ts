import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AirportsListComponent } from './airports-list/airports-list.component';
import { AirportDetailComponent } from './airport-detail/airport-detail.component';
import { LoginComponent } from './auth/login/login.component';
import { authGuard } from './auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: '/airports',
    pathMatch: 'full'
  },
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'airports',
    component: AirportsListComponent,
    canActivate: [authGuard]
  },
  {
    path: 'airport/:id',
    component: AirportDetailComponent,
    canActivate: [authGuard]
  },
  {
    path: '**',
    redirectTo: '/airports'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
