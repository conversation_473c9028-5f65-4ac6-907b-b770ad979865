{"version": 3, "sources": ["src/app/shared/airport-card/airport-card.component.scss"], "sourcesContent": [".airport-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  padding: 16px;\r\n  width: 100%;\r\n  max-width: 700px;\r\n  border: 1px solid #DDDDDD;\r\n  border-radius: 8px;\r\n  box-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.16);\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  background: white;\r\n\r\n  &:hover {\r\n    box-shadow: 2px 2px 8px 0px rgba(0, 0, 0, 0.2);\r\n    transform: translateY(-1px);\r\n  }\r\n}\r\n\r\n.airport-name {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-self: stretch;\r\n  \r\n  .airport-description {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    line-height: 1.364;\r\n    color: #4D4D4D;\r\n    margin-bottom: 4px;\r\n  }\r\n  \r\n  .city-code {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-weight: 700;\r\n    font-size: 12px;\r\n    line-height: 1.364;\r\n    color: #CCCCCC;\r\n  }\r\n}\r\n\r\n.airport-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-self: stretch;\r\n  \r\n  .detail-label {\r\n    font-family: 'Nunito', sans-serif;\r\n    font-weight: 700;\r\n    font-size: 12px;\r\n    line-height: 1.364;\r\n    color: #4D4D4D;\r\n  }\r\n}\r\n\r\n// Responsive design\r\n@media (max-width: 768px) {\r\n  .airport-card {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .airport-name .airport-description {\r\n    font-size: 14px;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,WAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,cAAA,IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;AACA,cAAA;;AAEA,CAdF,YAcE;AACE,cAAA,IAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,aAAA,WAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,cAAA;;AAEA,CALF,aAKE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAdF,aAcE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,cAAA;;AAEA,CALF,YAKE,CAAA;AACE,eAAA,QAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GAjEF;AAkEI,aAAA;;AAGF,GAjDF,aAiDE,CA5CA;AA6CE,eAAA;;;", "names": []}