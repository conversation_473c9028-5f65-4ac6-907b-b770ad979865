// Colores del diseño
$color-primary-a: #FFCC00;
$color-primary-b: #eae0c3;

$color-white: #FFF;
$gray-10: #F7F7F7;
$gray-15: #F3F3F3;
$gray-18: #E9E9E9;
$gray-20: #DDD; //color-primary-b--light
$gray-30: #CCC;
$gray-45: #999;
$gray-60: #4D4D4D; //color-primary-b
$gray-75: #333; // color-primary-b--dark
$color-black: #000;

// Estilos globales
html, body { 
  height: 100%; 
  background: $gray-10;
  margin: 0;
  padding: 0;
}

body { 
  font-family: '<PERSON>uni<PERSON>', 'Roboto', "Helvetica Neue", sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: $gray-75;
}

// Reset de márgenes para elementos comunes
h1, h2, h3, h4, h5, h6, p {
  margin: 0;
  padding: 0;
}

// Estilos para Material Design
.mat-mdc-card {
  border-radius: 8px !important;
}

.mat-mdc-button {
  font-family: 'Nunito', sans-serif !important;
  font-weight: 700 !important;
}

.mat-mdc-form-field {
  font-family: 'Nunito', sans-serif !important;
}

// Utilidades
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Responsive breakpoints
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
