<div class="login-container">
  <div class="login-card-container">
    <mat-card class="login-card">
      <mat-card-header>
        <mat-card-title>Iniciar <PERSON></mat-card-title>
        <mat-card-subtitle>Accede a Airports of the World</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Nombre de usuario</mat-label>
            <input matInput formControlName="username" placeholder="Ingresa tu nombre de usuario">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="username?.invalid && username?.touched">
              <span *ngIf="username?.errors?.['required']">El nombre de usuario es requerido</span>
              <span *ngIf="username?.errors?.['minlength']">Mínimo 3 caracteres</span>
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Contraseña</mat-label>
            <input matInput 
                   formControlName="securityKey" 
                   placeholder="Ingresa tu contraseña" 
                   [type]="hidePassword ? 'password' : 'text'">
            <button mat-icon-button matSuffix 
                    (click)="togglePasswordVisibility($event)" 
                    [attr.aria-label]="'Mostrar contraseña'" 
                    [attr.aria-pressed]="!hidePassword">
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="securityKey?.invalid && securityKey?.touched">
              <span *ngIf="securityKey?.errors?.['required']">La contraseña es requerida</span>
            </mat-error>
          </mat-form-field>

          <div class="error-message" *ngIf="errorMessage">
            {{ errorMessage }}
          </div>

          <button mat-raised-button color="primary" type="submit" class="login-button full-width">
            Iniciar Sesión
          </button>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>
