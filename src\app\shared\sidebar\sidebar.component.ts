import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, NavigationEnd } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { AirportsListService } from '../../airports-list/airports-list.service';
import { Airport } from '../../airports-list/airport';
import { Observable, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss'
})
export class SidebarComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('sidebarContainer', { static: false }) sidebarContainer!: ElementRef;
  airports$: Observable<Airport[]>;
  currentRoute: string = '';
  private destroy$ = new Subject<void>();

  constructor(
    private airportsService: AirportsListService,
    private router: Router
  ) {
    this.airports$ = this.airportsService.getAllAirports();
  }

  ngOnInit(): void {
    // Detect route changes and scroll to top
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe((event: NavigationEnd) => {
      this.currentRoute = event.url;
      window.scrollTo(0, 0);

      // Scroll to active item in mobile sidebar after route change
      setTimeout(() => {
        this.scrollToActiveItem();
      }, 100);
    });

    // Set initial route
    this.currentRoute = this.router.url;
  }

  ngAfterViewInit(): void {
    // Initial scroll to active item
    setTimeout(() => {
      this.scrollToActiveItem();
    }, 200);
  }

  navigateToAirport(airport: Airport): void {
    this.router.navigate(['/airport', airport.key]);
  }

  navigateToHome(): void {
    this.router.navigate(['/airports']);
  }

  isActive(route: string): boolean {
    return this.currentRoute === route;
  }

  isAirportActive(airportKey: string): boolean {
    return this.currentRoute === `/airport/${airportKey}`;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private scrollToActiveItem(): void {
    if (window.innerWidth <= 768 && this.sidebarContainer) {
      const activeElement = this.sidebarContainer.nativeElement.querySelector('.menu-item.active');
      if (activeElement) {
        const container = this.sidebarContainer.nativeElement;
        const scrollLeft = activeElement.offsetLeft - (container.clientWidth / 2) + (activeElement.clientWidth / 2);
        container.scrollTo({
          left: Math.max(0, scrollLeft),
          behavior: 'smooth'
        });
      }
    }
  }
}
