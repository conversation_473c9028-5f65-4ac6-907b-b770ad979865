import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { AirportsListService } from '../airports-list/airports-list.service';
import { Airport } from '../airports-list/airport';
import { HeaderComponent } from '../shared/header/header.component';
import { FooterComponent } from '../shared/footer/footer.component';
import { SidebarComponent } from '../shared/sidebar/sidebar.component';
import { AirportMetadataService } from '../services/airport-metadata.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-airport-detail',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule,
    HeaderComponent,
    FooterComponent,
    SidebarComponent
  ],
  templateUrl: './airport-detail.component.html',
  styleUrls: ['./airport-detail.component.scss']
})
export class AirportDetailComponent implements OnInit, OnDestroy {
  airport?: Airport;
  loading = false;
  error?: string;
  airportKey?: string;
  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private airportsService: AirportsListService,
    private metadataService: AirportMetadataService
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      this.airportKey = params['id'];
      if (this.airportKey) {
        this.loadAirportDetail(this.airportKey);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadAirportDetail(key: string): void {
    this.loading = true;
    this.error = undefined;

    this.airportsService.getAirport(key).subscribe({
      next: (airport) => {
        this.airport = airport;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Error al cargar el detalle del aeropuerto';
        this.loading = false;
      }
    });
  }

  getAirportDisplayName(): string {
    if (!this.airport) return '';
    return this.metadataService.getAirportTitle(this.airport.key);
  }

  getAirportImage(): string {
    if (!this.airport) return '';
    return this.metadataService.getAirportImage(this.airport.key);
  }

  getAirportDescription1(): string {
    if (!this.airport) return '';
    return this.metadataService.getAirportDescription1(this.airport.key);
  }

  getAirportDescription2(): string {
    if (!this.airport) return '';
    return this.metadataService.getAirportDescription2(this.airport.key);
  }

  goBack(): void {
    this.router.navigate(['/airports']);
  }

  retry(): void {
    if (this.airportKey) {
      this.loadAirportDetail(this.airportKey);
    }
  }
}
