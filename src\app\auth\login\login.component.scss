.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.login-card-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: white;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

.full-width {
  width: 100%;
}

.login-form .login-button.mat-mdc-raised-button {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 16px;
 background-color: #FFCC00;
 color: #000000;
 border: 2px solid #000000;
  
  &:hover {
   background-color: #FFD700;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 204, 0, 0.3);
  }
  
  &:active {
    transform: translateY(0);
    background-color: #E6B800 !important;
  }
  
  &:disabled {
    background-color: #F5F5F5 !important;
    color: #CCCCCC !important;
    border-color: #E0E0E0 !important;
    transform: none;
    box-shadow: none;
  }
  
  .mat-button-ripple {
    color: rgba(0, 0, 0, 0.1) !important;
  }
}

.error-message {
  color: #f44336;
  font-size: 14px;
  text-align: center;
  padding: 8px;
  background-color: #ffebee;
  border-radius: 4px;
  border: 1px solid #ffcdd2;
}

mat-card-header {
  text-align: center;
  margin-bottom: 20px;
}

mat-card-title {
  font-size: 24px;
  font-weight: 700;
  color: #333333;
  margin-bottom: 8px;
}

mat-card-subtitle {
  font-size: 14px;
  color: #666666;
}

mat-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-card {
    padding: 16px;
  }
}
