{"version": 3, "sources": ["src/app/auth/login/login.component.scss"], "sourcesContent": [".login-container {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  padding: 20px;\r\n}\r\n\r\n.login-card-container {\r\n  width: 100%;\r\n  max-width: 400px;\r\n}\r\n\r\n.login-card {\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  background: white;\r\n}\r\n\r\n.login-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.full-width {\r\n  width: 100%;\r\n}\r\n\r\n.login-button {\r\n  height: 48px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-top: 16px;\r\n}\r\n\r\n.error-message {\r\n  color: #f44336;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  padding: 8px;\r\n  background-color: #ffebee;\r\n  border-radius: 4px;\r\n  border: 1px solid #ffcdd2;\r\n}\r\n\r\nmat-card-header {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\nmat-card-title {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #333333;\r\n  margin-bottom: 8px;\r\n}\r\n\r\nmat-card-subtitle {\r\n  font-size: 14px;\r\n  color: #666666;\r\n}\r\n\r\nmat-form-field {\r\n  .mat-mdc-form-field-subscript-wrapper {\r\n    margin-top: 4px;\r\n  }\r\n}\r\n\r\n// Responsive design\r\n@media (max-width: 480px) {\r\n  .login-container {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .login-card {\r\n    padding: 16px;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,cAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,WAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,UAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;;AAGF;AACE,cAAA;AACA,iBAAA;;AAGF;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF;AACE,aAAA;AACA,SAAA;;AAIA,eAAA,CAAA;AACE,cAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GA1EF;AA2EI,aAAA;;AAGF,GAhEF;AAiEI,aAAA;;;", "names": []}