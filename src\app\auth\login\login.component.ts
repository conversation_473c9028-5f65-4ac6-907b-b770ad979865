import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  errorMessage = '';
  hidePassword = true; // Por defecto, la contraseña está oculta

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Si ya está logueado, redirigir a la lista de aeropuertos
    if (this.authService.isLoggedIn()) {
      this.router.navigate(['/airports']);
    }

    this.loginForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      securityKey: ['', [Validators.required, Validators.minLength(1)]]
    });
  }

  togglePasswordVisibility(event: Event): void {
    event.preventDefault(); // Prevenir el envío del formulario
    this.hidePassword = !this.hidePassword;
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      const { username, securityKey } = this.loginForm.value;
      
      if (this.authService.login(username, securityKey)) {
        this.router.navigate(['/airports']);
      } else {
        this.errorMessage = 'Error en el login. Por favor, verifica tus credenciales.';
      }
    } else {
      this.errorMessage = 'Por favor, completa todos los campos requeridos.';
    }
  }

  get username() { return this.loginForm.get('username'); }
  get securityKey() { return this.loginForm.get('securityKey'); }
}
