.footer-container {
  background-color: #000000;
  padding: 20px 254px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 56px;
}

.footer-content {
  width: 100%;
  max-width: 932px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 145px;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 16px;

  .link-item {
    font-family: 'Nunito', sans-serif;
    font-weight: 700;
    font-size: 12px;
    line-height: 1.364;
    color: white;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .separator {
    width: 1px;
    height: 16px;
    background-color: white;
  }
}

.footer-social {
  display: flex;
  align-items: center;
  gap: 16px;

  .social-button {
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }

    .social-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 20px 16px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .footer-links {
    justify-content: center;
    flex-wrap: wrap;
  }
} 