import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs';
import {Airport} from './airport';
import {AuthService} from '../auth/auth.service';
import { environment } from '../../environments/environment';

@Injectable({providedIn: 'root'})
export class AirportsListService {

    private baseUrl = environment.apiUrl;

    constructor(
        private http: HttpClient,
        private authService: AuthService
    ) {}

    private getHeaders(): HttpHeaders {
        const securityKey = this.authService.getSecurityKey();
        return new HttpHeaders({
            'Content-Type': 'application/json',
            'securityKey': securityKey || ''
        });
    }

    public getAllAirports(): Observable<Airport[]> {
        const url = `${this.baseUrl}/allAirports`;
        return this.http.get<Airport[]>(url, { headers: this.getHeaders() });
    }

    public getAirport(airportKey: string): Observable<Airport> {
        const url = `${this.baseUrl}/airport`;
        return this.http.post<Airport>(url, {key: airportKey}, { headers: this.getHeaders() });
    }
}
